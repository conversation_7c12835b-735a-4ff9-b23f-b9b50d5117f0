{"_comment1": "navbar.php", "client_logo": "Client <PERSON>", "search": "Search", "search_language": "Search Language", "change_language": "Change Language", "profile": "Profile", "logout": "Logout", "_comment2": "dashboard.php", "dashboard_welcome": "Welcome to the Dashboard", "dashboard_description": "This is your e-learning admin panel.", "_comment3": "Footer.php", "company_name": "Deeplaxmi Communications", "all_rights_reserved": "All Rights Reserved.", "_comment4": "Sidebar.php", "dashboard": "Dashboard", "manage_portal": "Manage Portal", "my_courses": "My Courses", "search_courses": "Search Courses", "_comment5": "Manage_portal.php", "user_details": "User Details", "course_details": "Course Details", "social": "Social", "settings": "Settings", "user_management": "User Management", "create_edit_remove_user": "Create, Edit, Remove User", "user_settings": "User Settings", "manage_user_roles_permissions": "Manage user roles and permissions", "course_creation": "Course Creation", "create_courses": "Create eLearning, Classroom, and Assessment Courses", "course_module_creation": "Course Module Creation", "organize_courses": "Organize Courses into Sections", "course_categories": "Course Categories", "category": "Category", "manage_categories": "Add and Manage Course Categories", "sub_category": "Sub-Category", "define_sub_categories": "Define Course Sub-Categories", "course_content": "Course Content", "vlr": "VLR - Virtual Learning Repository", "manage_vlr": "Manage SCORM, Assessments, Videos & More", "social_coming_soon": "Social settings section coming soon...", "_comment_opinion_polls": "Opinion Poll Management", "opinion_polls": "Opinion Polls", "opinion_poll_management": "Opinion Poll Management", "create_new_poll": "Create New Poll", "poll_title": "Poll Title", "poll_description": "Poll Description", "poll_type": "Poll Type", "single_choice": "Single Choice", "multiple_choice": "Multiple Choice", "target_audience": "Target Audience", "global": "Global (All Users)", "course_specific": "Course Specific", "group_specific": "Group Specific", "start_datetime": "Start Date & Time", "end_datetime": "End Date & Time", "show_results": "Show Results", "after_vote": "After Vote", "after_end": "After Poll Ends", "admin_only": "Admin Only", "allow_anonymous": "Allow Anonymous Voting", "allow_vote_change": "Allow Vote Changes", "poll_questions": "Poll Questions", "add_question": "Add Question", "question_text": "Question Text", "answer_options": "Answer Options", "add_option": "Add Option", "create_poll": "Create Poll", "edit_poll": "Edit Poll", "delete_poll": "Delete Poll", "activate_poll": "Activate Poll", "pause_poll": "Pause Poll", "resume_poll": "Resume Poll", "archive_poll": "Archive Poll", "view_results": "View Results", "poll_status_draft": "Draft", "poll_status_active": "Active", "poll_status_paused": "Paused", "poll_status_ended": "Ended", "poll_status_archived": "Archived", "poll_created_successfully": "Opinion poll created successfully!", "poll_updated_successfully": "Opinion poll updated successfully!", "poll_deleted_successfully": "Opinion poll deleted successfully!", "poll_status_updated": "Poll status updated successfully!", "no_polls_found": "No polls found", "search_polls": "Search polls by title or description...", "all_statuses": "All Statuses", "all_types": "All Types", "all_audiences": "All Audiences", "all_dates": "All Dates", "currently_active": "Currently Active", "upcoming": "Upcoming", "ended": "Ended", "clear_filters": "Clear Filters", "showing_results": "Showing {count} result{plural}", "with_filters_applied": "with filters applied", "for_search": "for search", "poll_duration": "Duration", "poll_participation": "Participation", "poll_settings": "Settings", "created_by": "Created by", "total_votes": "votes", "unique_voters": "users", "poll_live": "Live", "poll_upcoming": "Upcoming", "poll_ended": "Ended", "_comment_opinion_poll_items": "Opinion Poll Item Names for Confirmations", "item.opinion_poll": "opinion poll \"{title}\"", "confirmation.delete.opinion_poll": "Are you sure you want to delete this opinion poll?", "confirmation.status_change.opinion_poll": "Are you sure you want to change the status of this poll?", "settings_coming_soon": "Settings section coming soon...", "_comment6": "user_management.php", "user_management_title": "User Management", "filters_profile_id": "Profile ID", "filters_full_name": "Full Name", "filters_email": "Email", "filters_contact_number": "Contact Number", "filters_user_status": "User Status", "filters_locked_status": "Locked Status", "filters_search_placeholder": "Search questions...", "buttons_add_user": "Add User", "buttons_add_user_tooltip": "Add a new user to the system", "buttons_import_user": "Import Users", "user_grid_profile_id": "Profile ID", "user_grid_full_name": "Full Name", "user_grid_email": "Email", "user_grid_contact_number": "Contact Number", "user_grid_user_status": "User Status", "user_grid_locked_status": "Locked Status", "user_grid_action": "Action", "user_grid_active": "Active", "user_grid_inactive": "Inactive", "user_grid_locked": "Locked", "user_grid_unlocked": "Unlocked", "user_grid_edit_user": "Edit User", "user_grid_lock_user": "Lock User", "user_grid_unlock_user": "Unlock User", "user_grid_delete_user": "Delete User", "user_grid_edit_disabled": "Super Admin cannot be edited", "user_grid_lock_disabled": "Super Admin cannot be locked", "user_grid_unlock_disabled": "Super Admin cannot be unlocked", "user_grid_delete_disabled": "Super Admin cannot be deleted", "user_grid_no_users_found": "No users found.", "user_grid_lock_confirm": "Are you sure you want to lock this user?", "user_grid_unlock_confirm": "Are you sure you want to unlock this user?", "user_grid_delete_confirm": "Are you sure you want to delete this user? This action is reversible.", "pagination_prev": "Previous", "pagination_next": "Next", "_comment7": "add_user.php", "add_user_title": "Add User", "edit_user_title": "Edit User", "update": "Update", "basic_details": "Basic Details", "additional_details": "Additional Details", "extra_details": "Extra Details", "profile_id": "Profile ID (Auto-Generated)", "full_name": "Full Name", "email": "Email", "contact_number": "Contact Number", "gender": "Gender", "select_gender": "Select Gender", "male": "Male", "female": "Female", "other": "Other", "dob": "Date of Birth", "user_role": "User Role", "select_user_role": "Select User Role", "admin": "Admin", "end_user": "End User", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "corporate_manager": "Corporate Manager", "hr_manager": "HR Manager", "team_lead": "Team Lead", "content_creator": "Content Creator", "profile_expiry_date": "Profile Expiry Date", "user_status": "User Status", "active": "Active", "inactive": "Inactive", "locked_status": "Locked Status", "locked": "Locked", "unlocked": "Unlocked", "leaderboard": "Appear on Leaderboard", "yes": "Yes", "no": "No", "profile_picture": "Profile Picture", "country": "Country", "select_country": "Select Country", "state": "State", "select_state": "Select State", "city": "City", "select_city": "Select City", "timezone": "Timezone", "select_timezone": "Select Timezone", "language": "Language", "select_language": "Select Language", "reports_to": "Reports To", "joining_date": "Joining Date", "retirement_date": "Retirement Date", "customised_1": "Customised 1", "customised_2": "Customised 2", "customised_3": "Customised 3", "customised_4": "Customised 4", "customised_5": "Customised 5", "customised_6": "Customised 6", "customised_7": "Customised 7", "customised_8": "Customised 8", "customised_9": "Customised 9", "customised_10": "Customised 10", "submit": "Submit", "cancel": "Cancel", "_comment8": "vlr.php", "vlr_title": "Virtual Learning Repository (VLR)", "non_scorm": "NON-SCORM", "assessment": "Assessment", "audio": "Audio", "video": "Video", "document": "Document", "image": "Image", "external_content": "External Content", "survey": "Survey", "feedback": "<PERSON><PERSON><PERSON>", "interactive_ai_content": "Interactive & AI Powered Content", "scorm": "SCORM", "add_scorm": "Add SCORM", "add_scorm_package": "Add SCORM Package", "close": "Close", "upload_scorm_zip": "Upload SCORM Zip File", "version": "Version", "language_support": "Language Support", "scorm_category": "SCORM Category", "select_scorm_type": "Select SCORM Type", "scorm_1_2": "SCORM 1.2", "scorm_2004": "SCORM 2004", "tincan_api_xapi": "Tin Can API (xAPI)", "cmi5": "CMI5", "time_limit": "Time Limit", "assessment_included": "Assessment Included", "scorm-1.2_content": "SCORM 1.2 Content", "scorm-2004_content": "SCORM 2004 Content", "tin-can-api_content": "Tin Can API (xAPI) Content", "cmi5_content": "CMI5 Content", "edit": "Edit", "delete": "Delete", "preview": "Preview", "delete_confirmation_scrom": "Are you sure you want to delete this SCORM package?", "no_scorm_found": "No SCORM packages found for this category.", "add": "Add", "documents": "Documents", "word_excel_ppt": "Word/Excel/PPT Files", "word_excel_ppt_desc": "Upload and manage Word, Excel, and PowerPoint files.", "ebook_manual": "E-Book & Manual", "ebook_manual_desc": "Upload and manage e-books and manuals.", "research_case_studies": "Research Paper & Case Studies", "research_case_studies_desc": "Upload and manage research papers and case studies.", "add_external_content": "Add External Content", "title": "Title", "version_number": "Version Number", "mobile_tablet_support": "Mobile & Tablet Support", "english": "English", "hindi": "Hindi", "marathi": "Marathi", "spanish": "Spanish", "french": "French", "german": "German", "chinese": "Chinese", "minutes": "minutes", "content_type": "Content Type", "youtube_vimeo": "YouTube & Vimeo Content", "linkedin_udemy": "LinkedIn Learning, Udemy, Coursera", "podcasts_audio": "Podcasts & Audio Lessons", "description": "Description", "tags_keywords": "Tags / Keywords", "add_tag_placeholder": "Add a tag and press Enter", "video_url": "Video URL", "thumbnail_preview": "Thumbnail Preview", "course_url": "Course URL", "platform_name": "Platform Name", "select": "Select", "article_url": "URL", "author_publisher": "Author/Publisher", "audio_source": "Audio Source", "upload_file": "Upload File", "audio_url": "Audio URL", "upload_audio": "Upload Audio", "speaker_host": "Speaker / Host", "youtube_vimeo_ul": "YouTube & Vimeo Integration", "linkedin_udemy_coursera_ul": "LinkedIn Learning, Udemy, Coursera", "web_links_blogs_ul": "Web Links & Blogs", "podcasts_audio_lessons_ul": "Podcasts & Audio Lessons", "linkedin_udemy_coursera": "LinkedIn Learning, Udemy, Coursera Content", "web_links_blogs": "Web Links & Blogs Content", "podcasts_audio_lessons": "Podcasts & Audio Lessons Content", "confirm_delete": "Are you sure you want to delete this external content?", "no_external_content": "No External Content found for this category.", "adaptive_learning": "Adaptive Learning Content", "chatbots_virtual_assistants": "Chatbots & Virtual Assistants", "ar_vr": "Augmented Reality (AR) / Virtual Reality (VR)", "adaptive_learning_desc": "Manage and customize adaptive learning content.", "chatbots_virtual_assistants_desc": "Manage AI-powered chatbots and virtual assistants.", "ar_vr_desc": "Manage AR and VR-based interactive learning experiences.", "add_document": "Add Document", "document.modal.add": "Add Document", "document.modal.edit": "Edit Document", "select_category": "Select Category", "category.word_excel_ppt": "Word/Excel/PPT Files", "category.ebook_manual": "E-Book & Manual", "category.research_paper": "Research Paper & Case Studies", "upload_file.word_excel_ppt": "Upload File (.docx, .xlsx, .pptx, .pdf)", "upload_file.ebook_manual": "Upload File (.pdf, .epub, .mobi)", "upload_file.research": "Upload File (.pdf, .docx)", "authors": "Authors", "publication_date": "Publication Date", "reference_links": "Reference Links", "no_languages_available": "No languages available", "mobile_support": "Mobile Support", "add_assessment": "Add Assessment", "add_questions": "Add Assessment Questions", "add_assessment_questions": "Add Assessment Questions", "import_assessment_questions": "Import Assessment Questions", "import_assessment_questions_tooltip": "Import assessment questions from Excel file", "_comment9": "add_user_validation.js", "validation.full_name_required": "Full Name is required", "validation.email_required": "Email is required", "validation.email_invalid": "Enter a valid email address", "validation.contact_required": "Contact Number is required", "validation.contact_invalid": "Enter a valid 10-digit phone number", "validation.dob_required": "Date of Birth is required", "validation.dob_future": "Date of Birth cannot be in the future", "validation.user_role_required": "User Role is required", "validation.profile_expiry_invalid": "Profile Expiry Date cannot be before today", "validation.image_format": "Only JPG and PNG files are allowed", "validation.image_size": "File size should not exceed 5MB", "_comment10": "scorm_validation.js", "validation.scorm_title_required": "SCORM Title is required", "validation.scorm_zip_required": "SCORM Zip File is required", "validation.version_required": "Version number is required.", "validation.scorm_category_required": "Please select a SCORM Category", "scorm.modal.edit": "Edit SCORM Package", "scorm.modal.add": "Add SCORM Package", "_comment11": "external_content_validation.js", "validation.required.title": "Title is required", "validation.required.content_type": "Please select a content type", "validation.required.version": "Version is required", "validation.required.tags": "Tags/Keywords are required", "validation.required.url": "URL is required", "validation.invalid.url": "Enter a valid URL (e.g., https://example.com)", "error.form_not_found": "External Content Form NOT found!", "error.submit_button_missing": "Submit button NOT found! Check your HTML.", "validation.required.audio_file": "Please upload an audio file (MP3/WAV)", "validation.invalid.audio_file": "Invalid file type. Only MP3 and WAV are allowed.", "validation.required.thumbnail": "Please upload an image file (JPG, PNG, GIF, WebP).", "validation.invalid.thumbnail": "Invalid file type. Only JPG, PNG, GIF, and WebP are allowed.", "validation.file_size_exceeded": "File size exceeds the allowed limit.", "_comment12": "document_validation.js", "error.document_form_not_found": "Document form not found", "validation.document_title_required": "Document title is required.", "validation.document_category_required": "Document category is required.", "validation.document_file_required": "A document file is required.", "validation.invalid_file_format": "Invalid file format.", "validation.tags_required": "At least one tag is required.", "document.category.word_excel_ppt": "Word/Excel/PPT Files", "document.category.ebook_manual": "E-Book & Manual", "document.category.research_paper": "Research Paper & Case Studies", "_comment13": "document_package.js", "_comment14": "assessment_package.js", "confirm_delete_document": "Are you sure you want to delete this Document content?", "no_documents_available": "No documents available.", "confirm_delete_client": "Are you sure you want to delete this client?", "_comment15": "add_assessment.php", "question_management_title": "Assessment Question Management", "filters_select_options": "Select filter options", "filters_question_type": "Question Type", "filters_difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filters_tags": "Tags", "filters_created_by": "Created By", "filters_search": "Search", "buttons_add_question": "Add Question", "buttons_add_question_tooltip": "Click to add a new question", "buttons_import_user_tooltip": "Click to import users from a file", "question_grid_title": "Title", "question_grid_type": "Type", "question_grid_difficulty": "Difficulty Level", "question_grid_tags": "Tags", "question_grid_created_by": "Created By", "question_grid_actions": "Actions", "question_grid_edit": "Edit this question", "question_grid_delete": "Delete this question", "question_grid_delete_confirm": "Are you sure you want to delete this question?", "question_grid_no_questions_found": "No questions found.", "comment_16": "add_assessment_question.php", "edit_assessment_question_title": "Edit Assessment Question", "add_assessment_question_title": "Add Assessment Question", "question_label": "Question", "type_and_press_enter": "Type and press enter...", "competency_skills": "Competency Skills", "question_level": "Question Level", "low": "Low", "medium": "Medium", "hard": "Hard", "marks_per_question": "Marks Per Question", "status": "Status", "question_type": "Question Type", "objective": "Objective", "subjective": "Subjective", "how_many_answer_options": "How Many Answer Options", "question_media_type": "Question Media Type", "text": "Text", "upload_media": "Upload Media", "option": "Option", "correct_answer": "Correct Answer", "comment_17": "add_question_validation.js", "assessment.validation.required_field": "{field} is required.", "_comment18": "Import Assessment Questions", "import_instructions_title": "How to Import Assessment Questions", "import_step_1": "Download the appropriate template (Objective or Subjective) below", "import_step_2": "Fill in your questions data in the Excel template", "import_step_3": "Select the question type and upload your completed Excel file", "import_step_4": "Click Import Questions to bulk import all questions into the database", "objective_questions": "Objective Questions", "subjective_questions": "Subjective Questions", "objective_template_description": "For multiple choice questions with options and correct answers", "subjective_template_description": "For descriptive questions without predefined options", "download_template": "Download Template", "select_excel_file": "Select Excel File", "excel_file_requirements": "Only .xlsx and .xls files are allowed. Maximum file size: 10MB", "select_question_type": "Select Question Type", "important_note": "Important Note", "import_warning_message": "Please ensure your Excel file follows the template format exactly. Invalid data may cause import errors.", "import_questions": "Import Questions", "assessment.validation.correct_answer_required": "At least one option must be marked as the correct answer.", "assessment.validation.media_required": "Upload Media is required.", "assessment.validation.invalid_media_type": "Only {allowed} files are allowed for {type}.", "assessment.validation.media_size_exceeded": "File size must be less than or equal to 5MB.", "assessment.validation.tags_required": "Tags/keywords are required.", "comment_18": "assessment.php", "assessment.modal.add_title": "Add Assessment", "assessment.field.title": "Assessment Title", "assessment.field.num_attempts": "Number of Attempts", "assessment.field.passing_percentage": "Passing Percentage (%)", "assessment.field.time_limit": "Time Limit (in minutes)", "assessment.field.negative_marking": "Negative Marking", "assessment.field.negative_percentage": "Negative Marking Percentage", "assessment.placeholder.select_negative_percentage": "Select Negative Marking Percentage", "assessment.field.type": "Assessment Type", "assessment.type.fixed": "Fixed", "assessment.type.dynamic": "Dynamic", "assessment.field.num_questions_to_display": "Number of Questions to Display", "assessment.button.add_question": "Add Question", "_comment18a": "assessment_validation.js", "assessment.validation.title_required": "Title is required.", "assessment.validation.passing_percentage_invalid": "Passing percentage must be between 0 and 100.", "assessment.validation.negative_percentage_required": "Percentage is required.", "assessment.validation.num_questions_required": "This field requires a numeric value.", "assessment.validation.num_questions_exceeds": "Cannot exceed {count} selected question{plural}.", "assessment.validation.questions_required": "At least one question must be selected.", "assessment.table.question_title": "Question Title", "assessment.table.tags": "Tags / Keywords", "assessment.table.marks": "Marks", "assessment.table.type": "Type", "select_questions": "Select Questions", "search_questions": "Search questions...", "loading": "Loading", "show_10": "Show 10", "show_25": "Show 25", "show_50": "Show 50", "show_75": "Show 75", "show_100": "Show 100", "refresh": "Refresh", "question_title": "Question Title", "marks": "Marks", "type": "Type", "loop_selected_questions": "Loop Selected Questions", "assessments": "Assessments", "delete_confirmation": "Are you sure you want to delete this assessment?", "no_assessments_found": "No assessments found.", "no_assessment_found": "No assessments found.", "_comment19": "survey.php", "survey.modal.add_title": "Add Survey", "survey.modal.edit_title": "Edit Survey", "survey.field.title": "Survey Title", "survey.button.add_question": "Add Question", "survey.table.question_title": "Question Title", "survey.table.tags": "Tags / Keywords", "survey.table.type": "Type", "survey.select_questions": "Select Questions", "survey.question_title": "Question Title", "surveys": "Surveys", "add_survey": "Add Survey", "add_survey_questions": "Add Survey Questions", "no_surveys_found": "No surveys found.", "_comment19a": "add_survey.php", "survey.question_management_title": "Survey Question Management", "survey.add_question_title": "Add Survey Question", "survey.edit_question_title": "Edit Survey Question", "survey.question_title_field": "Survey Question Title", "survey.question_type": "Question Type", "survey.rating_scale": "Rating Scale", "survey.rating_symbol": "Rating Symbol", "survey.multi_choice": "Multiple Choice", "survey.checkbox": "Checkbox", "survey.short_answer": "Short Answer", "survey.long_answer": "Long Answer", "survey.dropdown": "Dropdown", "survey.upload": "Upload", "survey.rating": "Rating", "survey.option": "Option", "survey.add_option": "Add Option", "survey.remove_option": "Remove Option", "survey.submit_question": "Submit Survey Question", "survey.question_saved": "Survey question saved successfully.", "survey.question_updated": "Survey question updated successfully.", "survey.question_deleted": "Survey question deleted successfully.", "survey_question_management_title": "Survey Question Management", "buttons_add_survey_question": "Add Survey Question", "buttons_import_survey": "Import Survey", "buttons_import_survey_tooltip": "Click to import survey from a file", "survey_question_title": "Survey Question Title", "survey_question_type": "Question Type", "survey_rating_scale": "Rating Scale", "symbol_star": "Star", "symbol_thumb": "Thumb", "symbol_heart": "Heart", "question_type_multi_choice": "Multi Choice", "question_type_checkbox": "Checkbox", "question_type_short_answer": "Short Answer", "question_type_long_answer": "Long Answer", "question_type_dropdown": "Dropdown", "question_type_upload": "Upload", "question_type_rating": "Rating", "buttons_submit_survey_question": "Submit Survey Question", "survey_grid_title": "Title", "survey_grid_type": "Type", "survey_grid_tags": "Tags", "survey_grid_actions": "Actions", "survey_grid_edit": "Edit this question", "survey_grid_delete": "Delete this question", "survey_grid_delete_confirm": "Are you sure you want to delete this question?", "survey_grid_no_questions_found": "No questions found.", "_comment20": "feedback.php", "feedback.modal.add_title": "<PERSON><PERSON>", "feedback.modal.edit_title": "<PERSON>", "feedback.field.title": "Feedback Title", "feedback.button.add_question": "Add Question", "feedback.table.question_title": "Question Title", "feedback.table.tags": "Tags / Keywords", "feedback.table.type": "Type", "feedback.select_questions": "Select Questions", "feedback.question_title": "Question Title", "feedbacks": "Feedbacks", "add_feedback": "<PERSON><PERSON>", "add_feedback_questions": "<PERSON><PERSON> Questions", "no_feedback_found": "No feedback found.", "_comment20a": "add_feedback_question.php", "feedback.question_management_title": "Feedback Question Management", "feedback.add_question_title": "Add <PERSON>back Question", "feedback.edit_question_title": "Edit Feedback Question", "feedback.question_title_field": "Feedback Question Title", "feedback.question_type": "Question Type", "feedback.rating_scale": "Rating Scale", "feedback.rating_symbol": "Rating Symbol", "feedback.multi_choice": "Multiple Choice", "feedback.checkbox": "Checkbox", "feedback.short_answer": "Short Answer", "feedback.long_answer": "Long Answer", "feedback.dropdown": "Dropdown", "feedback.upload": "Upload", "feedback.rating": "Rating", "feedback.option": "Option", "feedback.add_option": "Add Option", "feedback.remove_option": "Remove Option", "feedback.submit_question": "Submit <PERSON><PERSON><PERSON> Question", "feedback.question_saved": "Feedback question saved successfully.", "feedback.question_updated": "Feedback question updated successfully.", "feedback.question_deleted": "Feedback question deleted successfully.", "feedback_question_management_title": "Feedback Question Management", "buttons_add_feedback_question": "Add <PERSON>back Question", "buttons_import_feedback": "Import <PERSON>", "buttons_import_feedback_tooltip": "Click to import feedback from a file", "feedback_question_title": "Feedback Question Title", "feedback_question_type": "Question Type", "feedback_rating_scale": "Rating Scale", "upload_image_video_pdf": "Upload image, video, or PDF", "validation_required": "This field is required", "buttons_submit_feedback_question": "Submit <PERSON><PERSON><PERSON> Question", "buttons_cancel": "Cancel", "buttons_close": "Close", "add_tag": "Add Tag", "feedback_grid_title": "Title", "feedback_grid_type": "Type", "feedback_grid_tags": "Tags", "feedback_grid_actions": "Actions", "feedback_grid_edit": "Edit this question", "feedback_grid_delete": "Delete this question", "feedback_grid_delete_confirm": "Are you sure you want to delete this question?", "feedback_grid_no_questions_found": "No questions found.", "_comment21": "audio.php", "audio.modal.add_title": "Add Audio", "audio.modal.edit_title": "Edit Audio", "audio.field.title": "Audio Title", "audio.field.file": "Audio File", "audio.field.duration": "Duration", "audio.field.speaker": "Speaker", "audios": "Audios", "add_audio": "Add Audio", "add_audio_package": "Add Audio Package", "no_audio_found": "No audio found.", "audio.package_saved": "Audio package saved successfully.", "audio.package_updated": "Audio package updated successfully.", "audio.package_deleted": "Audio package deleted successfully.", "audio.upload_audio_file": "Upload Audio File", "audio.select_audio_file": "Select Audio File", "_comment22": "video.php", "video.modal.add_title": "Add Video", "video.modal.edit_title": "Edit Video", "video.field.title": "Video Title", "video.field.file": "Video File", "video.field.duration": "Duration", "video.field.resolution": "Resolution", "videos": "Videos", "add_video": "Add Video", "add_video_package": "Add Video Package", "no_videos_found": "No videos found.", "no_video_found": "No videos found.", "video.package_saved": "Video package saved successfully.", "video.package_updated": "Video package updated successfully.", "video.package_deleted": "Video package deleted successfully.", "video.upload_video_file": "Upload Video File", "video.select_video_file": "Select Video File", "_comment23": "image.php", "image.modal.add_title": "Add Image", "image.modal.edit_title": "Edit Image", "image.field.title": "Image Title", "image.field.file": "Image File", "image.field.resolution": "Resolution", "image.field.alt_text": "Alt Text", "images": "Images", "add_image": "Add Image", "add_image_package": "Add Image Package", "no_images_found": "No images found.", "no_image_found": "No images found.", "_comment27": "Settings Management", "add_interactive_content": "Add Interactive Content", "add_interactive_package": "Add Interactive Package", "interactive_ai_tutoring": "AI Tutoring", "interactive_adaptive_learning_desc": "Personalized learning experiences that adapt to individual learner needs", "interactive_ai_tutoring_desc": "AI-powered tutoring systems for personalized guidance", "interactive_ar_vr_desc": "Augmented and Virtual Reality immersive learning experiences", "interactive.field.title": "Content Title", "interactive.field.content_type": "Content Type", "interactive.field.description": "Description", "interactive.field.version": "Version", "interactive.field.language": "Language", "interactive.field.time_limit": "Time Limit (minutes)", "interactive.field.mobile_support": "Mobile Support", "interactive.field.content_url": "Content URL", "interactive.field.embed_code": "Embed Code", "interactive.field.ai_model": "AI Model", "interactive.field.interaction_type": "Interaction Type", "interactive.field.difficulty_level": "Difficulty Level", "interactive.field.learning_objectives": "Learning Objectives", "interactive.field.prerequisites": "Prerequisites", "interactive.field.content_file": "Content File", "interactive.field.thumbnail_image": "Thumbnail Image", "interactive.field.vr_platform": "VR Platform", "interactive.field.ar_platform": "AR Platform", "interactive.field.device_requirements": "Device Requirements", "interactive.field.tutor_personality": "Tutor Personality", "interactive.field.response_style": "Response Style", "interactive.field.knowledge_domain": "Knowledge Domain", "interactive.field.adaptation_algorithm": "Adaptation Algorithm", "interactive.field.assessment_integration": "Assessment Integration", "interactive.field.progress_tracking": "Progress Tracking", "interactive.placeholder.select_content_type": "Select Content Type", "interactive.placeholder.select_difficulty": "Select Difficulty Level", "interactive.placeholder.select_ai_model": "Select AI Model", "interactive.placeholder.select_vr_platform": "Select VR Platform", "interactive.placeholder.select_ar_platform": "Select AR Platform", "interactive.placeholder.select_tutor_personality": "Select Tutor Personality", "interactive.placeholder.select_response_style": "Select Response Style", "interactive.placeholder.select_adaptation_algorithm": "Select Adaptation Algorithm", "interactive.difficulty.beginner": "<PERSON><PERSON><PERSON>", "interactive.difficulty.intermediate": "Intermediate", "interactive.difficulty.advanced": "Advanced", "interactive.ai_model.gpt4": "GPT-4", "interactive.ai_model.claude": "<PERSON>", "interactive.ai_model.gemini": "Gemini", "interactive.ai_model.custom": "Custom Model", "interactive.vr_platform.oculus": "Oculus", "interactive.vr_platform.htc_vive": "HTC Vive", "interactive.vr_platform.playstation_vr": "PlayStation VR", "interactive.vr_platform.web_vr": "WebVR", "interactive.ar_platform.arcore": "ARCore", "interactive.ar_platform.arkit": "ARKit", "interactive.ar_platform.web_ar": "WebAR", "interactive.tutor_personality.friendly": "Friendly", "interactive.tutor_personality.professional": "Professional", "interactive.tutor_personality.encouraging": "Encouraging", "interactive.tutor_personality.strict": "Strict", "interactive.response_style.formal": "Formal", "interactive.response_style.casual": "Casual", "interactive.response_style.conversational": "Conversational", "interactive.adaptation_algorithm.bayesian": "Bayesian Knowledge Tracing", "interactive.adaptation_algorithm.irt": "Item Response Theory", "interactive.adaptation_algorithm.ml": "Machine Learning", "interactive.interaction_type.chat": "Chat Interface", "interactive.interaction_type.simulation": "Simulation", "interactive.interaction_type.game": "Gamification", "interactive.interaction_type.quiz": "Interactive Quiz", "no_interactive_content_found": "No interactive content found.", "image.package_saved": "Image package saved successfully.", "image.package_updated": "Image package updated successfully.", "image.package_deleted": "Image package deleted successfully.", "image.upload_image_file": "Upload Image File", "image.select_image_file": "Select Image File", "_comment24": "Package-specific validation messages", "survey.validation.title_required": "Survey title is required.", "survey.validation.question_type_required": "Survey question type is required.", "feedback.validation.title_required": "Feedback title is required.", "feedback.validation.question_type_required": "Feedback question type is required.", "audio.validation.title_required": "Audio title is required.", "audio.validation.file_required": "Audio file is required.", "video.validation.title_required": "Video title is required.", "video.validation.file_required": "Video file is required.", "image.validation.title_required": "Image title is required.", "image.validation.file_required": "Image file is required.", "_comment25": "Common success/error messages", "success.saved": "Saved successfully!", "success.updated": "Updated successfully!", "success.deleted": "Deleted successfully!", "error.save_failed": "Failed to save.", "error.update_failed": "Failed to update.", "error.delete_failed": "Failed to delete.", "error.not_found": "Item not found.", "error.unauthorized": "Unauthorized access.", "_comment_confirmations": "Confirmation Modal Messages", "confirmation.delete.title": "Delete Confirmation", "confirmation.lock.title": "Lock Confirmation", "confirmation.unlock.title": "Unlock Confirmation", "confirmation.delete.message": "Are you sure you want to delete this {item}?", "confirmation.lock.message": "Are you sure you want to lock this {item}?", "confirmation.unlock.message": "Are you sure you want to unlock this {item}?", "confirmation.delete.subtext": "This action is not reversible.", "confirmation.lock.subtext": "This will prevent the user from logging in.", "confirmation.unlock.subtext": "This will allow the user to log in again.", "confirmation.delete.button": "Delete", "confirmation.lock.button": "Lock", "confirmation.unlock.button": "Unlock", "confirmation.cancel.button": "Cancel", "_comment_items": "Item Names for Confirmations", "item.user": "user \"{name}\"", "item.scorm_package": "SCORM package \"{title}\"", "item.non_scorm_package": "non-SCORM package \"{title}\"", "item.assessment": "assessment \"{title}\"", "item.audio_package": "audio package \"{title}\"", "item.video_package": "video package \"{title}\"", "item.image_package": "image package \"{title}\"", "item.document": "document \"{title}\"", "item.external_content": "external content \"{title}\"", "item.interactive_content": "interactive content \"{title}\"", "item.survey": "survey \"{title}\"", "item.feedback": "feedback \"{title}\"", "item.question": "question \"{title}\"", "item.assessment_question": "assessment question \"{title}\"", "item.survey_question": "survey question \"{title}\"", "item.feedback_question": "feedback question \"{title}\"", "item.organization": "organization \"{name}\"", "organization_management": "Organization Management", "add_organization": "Add Organization", "search_organizations": "Search organizations...", "no_organizations_found": "No organizations found", "client_code": "Client Code", "users": "users", "plan": "Plan", "created": "Created", "manage_users": "Manage Users", "analytics": "Analytics", "error.invalid_request": "Invalid request.", "_comment26": "Client Management", "client_management": "Client Management", "client_management_title": "Client Management", "client_management_description": "Manage client organizations and their settings", "clients_all_statuses": "All Statuses", "clients_active": "Active", "clients_inactive": "Inactive", "clients_suspended": "Suspended", "clients_search_placeholder": "Search clients...", "clients_search_title": "Search clients", "clients_clear_filters": "Clear Filters", "clients_clear_filters_title": "Clear all filters", "clients_add_client": "Add Client", "clients_add_client_title": "Add new client", "clients_no_clients_found": "No clients found", "clients_loading": "Loading clients...", "clients_client_id": "Client ID", "clients_users": "Users", "clients_admin_limit": "<PERSON><PERSON>", "clients_roles": "roles", "clients_features": "Features", "clients_reports": "Reports", "clients_themes": "Themes", "clients_sso": "SSO", "clients_created": "Created", "clients_edit_title": "Edit", "clients_manage_users_title": "Manage Users", "clients_statistics_title": "Statistics", "clients_delete_title": "Delete", "clients_pagination_label": "Clients pagination", "clients_add_modal_title": "Add New Client", "clients_edit_modal_title": "Edit Client", "clients_basic_information": "Basic Information", "clients_client_name": "Client Name", "clients_client_name_required": "Client Name", "clients_client_code": "Client Code", "clients_client_code_required": "Client Code", "clients_client_code_placeholder": "e.g., ACME_CORP", "clients_client_code_help": "Unique identifier for the client (uppercase, no spaces, use underscores)", "clients_custom_field_creation": "Custom Field Creation", "clients_custom_field_creation_help": "Allow this client to create custom fields for user profiles", "clients_maximum_users": "Maximum Users", "clients_maximum_users_required": "Maximum Users", "clients_maximum_users_placeholder": "Enter number of users", "clients_status": "Status", "clients_configuration_settings": "Configuration Settings", "clients_reports_enabled": "Reports", "clients_theme_color_setting": "Theme Color Setting", "clients_sso_login": "SSO Login", "clients_admin_role_limit": "Admin Role Limit", "clients_admin_role_limit_required": "Admin Role Limit", "clients_admin_role_limit_placeholder": "Enter number of admin roles allowed", "clients_admin_role_limit_help": "Maximum number of admin users allowed", "clients_branding": "Branding", "clients_client_logo": "Client <PERSON>", "clients_client_logo_required": "Client <PERSON>", "clients_logo_help": "Upload PNG, JPG, or GIF (max 5MB)", "clients_logo_help_edit": "Upload PNG, JPG, or GIF (max 5MB) - Leave empty to keep current logo", "clients_description": "Description", "clients_description_placeholder": "Brief description of the client...", "clients_current_logo": "Current logo:", "clients_yes": "Yes", "clients_no": "No", "clients_cancel": "Cancel", "clients_create_client": "Create Client", "clients_update_client": "Update Client", "clients_close": "Close", "user_configuration": "User Configuration", "system_configuration": "System Configuration", "general_settings": "General Settings", "security_settings": "Security Settings", "user_roles_permissions": "User Roles & Permissions", "system_wide_settings": "Configure system-wide settings", "security_configuration": "Configure security settings", "create_edit_delete_custom_fields": "Create, edit, and delete custom fields for user profiles", "manage_custom_fields": "Manage Custom Fields", "manage_custom_fields_description": "Configure custom fields for user profiles and forms", "_comment27a": "Custom Fields Management", "custom_fields_management": "Custom Fields Management", "custom_fields_title": "Manage Custom Fields", "custom_fields_description": "Create and manage custom fields for user profiles", "custom_fields_create_button": "Create Custom Field", "custom_fields_create_title": "Create Custom Field", "custom_fields_edit_title": "Edit Custom Field", "custom_fields_field_name": "Field Name", "custom_fields_field_name_required": "Field Name", "custom_fields_field_name_placeholder": "Enter field name (e.g., employee_id)", "custom_fields_field_label": "Field Label", "custom_fields_field_label_required": "Field Label", "custom_fields_field_label_placeholder": "Enter field label (e.g., Employee ID)", "custom_fields_field_type": "Field Type", "custom_fields_field_type_required": "Field Type", "custom_fields_field_type_text": "Text Input", "custom_fields_field_type_textarea": "Text Area", "custom_fields_field_type_select": "Dropdown", "custom_fields_field_type_radio": "Radio Buttons", "custom_fields_field_type_checkbox": "Checkboxes", "custom_fields_field_type_file": "File Upload", "custom_fields_field_type_date": "Date Picker", "custom_fields_field_type_number": "Number Input", "custom_fields_field_type_email": "Email Input", "custom_fields_field_type_phone": "Phone Input", "custom_fields_field_options": "Field Options", "custom_fields_field_options_placeholder": "Enter options (one per line)\nOption 1\nOption 2\nOption 3", "custom_fields_field_options_help": "Enter each option on a new line (for dropdown, radio, checkbox fields)", "custom_fields_is_required": "Required Field", "custom_fields_is_active": "Active", "custom_fields_list": "Custom Fields List", "custom_fields_search": "Search custom fields...", "search_custom_fields": "Search custom fields", "filter_by_client": "Filter by Client", "all_clients": "All Clients", "all_status": "All Status", "all_fields": "All Fields", "active_fields": "Active Fields", "inactive_fields": "Inactive Fields", "required_fields": "Required <PERSON>", "optional_fields": "Optional Fields", "fields_in_use": "Fields in Use", "unused_fields": "Unused Fields", "showing_fields_for_client": "Showing fields for client", "showing_all_custom_fields": "Showing all custom fields", "no_custom_fields_found": "No custom fields found", "create_first_custom_field": "Create your first custom field to get started", "custom_fields_no_fields": "No custom fields found", "custom_fields_no_fields_description": "Create your first custom field to get started", "field_name": "Field Name", "field_label": "Field Label", "field_type": "Field Type", "required": "Required", "optional": "Optional", "usage": "Usage", "client": "Client", "actions": "Actions", "fields": "fields", "unused": "Unused", "clear": "Clear", "edit_custom_field": "Edit Custom Field", "delete_custom_field": "Delete Custom Field", "activate_custom_field": "Activate Custom Field", "deactivate_custom_field": "Deactivate Custom Field", "cannot_delete_field_in_use": "Cannot delete field that is currently in use", "deactivate_before_delete": "Deactivate field before deletion", "confirm_delete_custom_field": "Are you sure you want to delete this custom field?", "confirm_activate_custom_field": "Are you sure you want to activate this custom field?", "confirm_deactivate_custom_field": "Are you sure you want to deactivate this custom field?", "custom_fields_actions": "Actions", "custom_fields_edit": "Edit", "custom_fields_delete": "Delete", "custom_fields_delete_confirm": "Are you sure you want to delete this custom field?", "custom_fields_delete_warning": "This will also delete all user data for this field.", "custom_fields_activate": "Activate", "custom_fields_deactivate": "Deactivate", "custom_fields_activate_confirm": "Are you sure you want to activate this custom field?", "custom_fields_deactivate_confirm": "Are you sure you want to deactivate this custom field?", "custom_fields_status_active": "Active", "custom_fields_status_inactive": "Inactive", "custom_fields_usage_count": "Usage Count", "custom_fields_used_by_users": "Used by {count} user(s)", "custom_fields_unused": "Unused", "custom_fields_cannot_delete_active": "Cannot delete active field. Please deactivate first.", "custom_fields_cannot_delete_in_use": "Cannot delete field that is currently in use.", "custom_fields_deactivate_before_delete": "Deactivate field before deletion", "success.custom_field_activated": "Custom field activated successfully!", "success.custom_field_deactivated": "Custom field deactivated successfully!", "error.custom_field_activate_failed": "Failed to activate custom field.", "error.custom_field_deactivate_failed": "Failed to deactivate custom field.", "_comment28": "Client Management Validation", "js.validation.client_name_required": "Client name is required.", "js.validation.client_code_required": "Client code is required.", "js.validation.client_code_format": "Client code must contain only uppercase letters, numbers, and underscores.", "js.validation.max_users_required": "Maximum users is required.", "js.validation.max_users_numeric": "Maximum users must be a number.", "js.validation.max_users_minimum": "Maximum users must be at least 1.", "js.validation.admin_role_limit_required": "Admin role limit is required.", "js.validation.admin_role_limit_numeric": "Admin role limit must be a number.", "js.validation.admin_role_limit_minimum": "Admin role limit must be at least 1.", "js.validation.client_logo_required": "Client logo is required.", "js.validation.logo_format_invalid": "Logo must be PNG, JPG, or GIF format.", "js.validation.logo_size_exceeded": "Logo file size must be less than 5MB.", "js.validation.client_form_not_found": "Client Form NOT found!", "validation.client_name_required": "Client name is required.", "validation.client_code_required": "Client code is required.", "validation.client_code_unique": "Client code already exists. Please choose a different code.", "validation.client_code_format": "Client code must contain only uppercase letters, numbers, and underscores.", "validation.max_users_required": "Maximum users is required.", "validation.max_users_numeric": "Maximum users must be a number.", "validation.max_users_minimum": "Maximum users must be at least 1.", "validation.admin_role_limit_required": "Admin role limit is required.", "validation.admin_role_limit_numeric": "Admin role limit must be a number.", "validation.admin_role_limit_minimum": "Admin role limit must be at least 1.", "validation.logo_upload_failed": "Failed to upload logo. Please try again.", "validation.client_id_required": "Client ID is required.", "validation.client_not_found": "Client not found.", "error.client_update_failed": "An unexpected error occurred while updating client.", "error.client_delete_failed": "Failed to delete client.", "success.client_created": "Client created successfully!", "success.client_updated": "Client updated successfully!", "success.client_deleted": "Client deleted successfully!", "validation.field_name_required": "Field name is required.", "validation.field_label_required": "Field label is required.", "validation.field_type_required": "Field type is required.", "validation.field_name_exists": "A field with this name already exists.", "validation.field_id_required": "Field ID is required.", "validation.field_not_found": "Custom field not found.", "success.custom_field_created": "Custom field created successfully!", "success.custom_field_updated": "Custom field updated successfully!", "success.custom_field_deleted": "Custom field deleted successfully!", "error.custom_field_create_failed": "Failed to create custom field.", "error.custom_field_update_failed": "Failed to update custom field.", "error.custom_field_delete_failed": "Failed to delete custom field.", "error.invalid_request_method": "Invalid request method.", "_comment28a": "Content Preview Messages", "preview.audio_title": "Audio Preview", "preview.video_title": "Video Preview", "preview.image_title": "Image Preview", "preview.document_title": "Document Preview", "preview.external_title": "External Content Preview", "preview.no_description": "No description available", "preview.version": "Version", "preview.language": "Language", "preview.mobile_support": "Mobile Support", "preview.category": "Category", "preview.type": "Type", "preview.no_file_available": "No File Available", "preview.no_associated_file": "This document does not have an associated file.", "preview.open_new_tab": "Open in New Tab", "preview.document_preview_not_available": "Document Preview Not Available", "preview.cannot_preview_browser": "cannot be previewed directly in the browser.", "preview.download_document": "Download Document", "preview.unsupported_file_type": "Unsupported File Type", "preview.preview_not_available_filetype": "Preview not available for this file type", "preview.download_file": "Download File", "preview.video_not_available": "Video preview not available.", "preview.open_video": "Open Video", "preview.course_content": "Course Content", "preview.platform": "Platform", "preview.open_course": "Open Course", "preview.web_article_blog": "Web Article/Blog", "preview.author": "Author", "preview.read_article": "Read Article", "preview.podcast_audio_content": "Podcast/Audio Content", "preview.speaker": "Speaker", "preview.listen_audio": "Listen to Audio", "preview.preview_not_available": "Preview Not Available", "preview.content_type_cannot_preview": "This content type cannot be previewed.", "preview.unknown": "Unknown", "preview.image_not_found": "Image not found", "_comment28b": "JavaScript validation messages", "js.validation.title_required": "Title is required.", "js.validation.feedback_title_required": "Feedback title is required.", "js.validation.survey_title_required": "Survey title is required.", "js.validation.feedback_question_title_required": "Feedback Question Title is required.", "js.validation.survey_question_title_required": "Survey Question Title is required.", "js.validation.tags_required": "At least one tag is required.", "js.validation.questions_required": "Please add at least one feedback question.", "js.validation.survey_questions_required": "Please add at least one survey question.", "js.validation.option_empty": "Option cannot be empty.", "js.validation.option_required": "At least one option is required.", "js.validation.audio_title_required": "Audio title is required.", "js.validation.audio_file_required": "Audio file is required.", "js.validation.audio_file_size_exceeded": "Audio file size exceeds the maximum allowed limit of 10MB.", "js.validation.video_title_required": "Video title is required.", "js.validation.video_file_required": "Video file is required.", "js.validation.video_file_size_exceeded": "Video file size exceeds the maximum allowed limit of 500MB.", "js.validation.image_title_required": "Image title is required.", "js.validation.image_file_required": "Image file is required.", "js.validation.image_file_size_exceeded": "Image file size exceeds the maximum allowed limit of 50MB.", "js.validation.interactive_title_required": "Interactive content title is required.", "js.validation.interactive_content_type_required": "Content type is required.", "js.validation.interactive_version_required": "Version is required.", "js.validation.interactive_tags_required": "Tags are required.", "js.validation.interactive_url_invalid": "Please enter a valid URL (e.g., https://example.com).", "js.validation.version_required_numeric": "Version must be a valid number.", "js.validation.time_limit_numeric": "Time limit must be a valid number.", "validation.required.field": "This field is required.", "_comment29": "Non-SCORM Package Translations", "add_non_scorm": "Add Non-SCORM", "add_non_scorm_package": "Add Non-SCORM Package", "html5_content": "HTML5 Content", "flash_content": "Flash Content", "unity_content": "Unity Content", "custom_web_app": "Custom Web App", "mobile_app": "Mobile App", "no_nonscorm_content_found": "No non-SCORM content found.", "no_html5_content_found": "No HTML5 content found.", "no_flash_content_found": "No Flash content found.", "no_unity_content_found": "No Unity content found.", "no_custom_web_found": "No Custom Web App found.", "no_mobile_app_found": "No Mobile App found.", "nonscorm.field.external_dependencies": "External Dependencies", "nonscorm.field.completion_criteria": "Completion Criteria", "nonscorm.field.scoring_method": "Scoring Method", "nonscorm.scoring.none": "None", "nonscorm.scoring.points": "Points", "nonscorm.scoring.percentage": "Percentage", "nonscorm.scoring.pass_fail": "Pass/Fail", "html5-content_content": "HTML5 Content", "flash-content_content": "Flash Content", "unity-content_content": "Unity Content", "custom-web_content": "Custom Web App", "mobile-app_content": "Mobile App", "nonscorm.field.title": "Title", "nonscorm.field.content_type": "Content Type", "nonscorm.field.version": "Version", "nonscorm.field.language": "Language", "nonscorm.field.description": "Description", "nonscorm.field.time_limit": "Time Limit", "nonscorm.field.mobile_support": "Mobile Support", "nonscorm.field.content_url": "Content URL", "nonscorm.field.launch_file": "Launch File", "nonscorm.field.content_package": "Content Package", "nonscorm.field.thumbnail_image": "Thumbnail Image", "nonscorm.field.manifest_file": "Manifest File", "nonscorm.field.html5_framework": "HTML5 Framework", "nonscorm.field.responsive_design": "Responsive Design", "nonscorm.field.offline_support": "Offline Support", "nonscorm.field.flash_version": "Flash Version", "nonscorm.field.flash_security": "Flash Security", "nonscorm.field.unity_version": "Unity Version", "nonscorm.field.unity_platform": "Unity Platform", "nonscorm.field.unity_compression": "Unity Compression", "nonscorm.field.web_technologies": "Web Technologies", "nonscorm.field.browser_requirements": "Browser Requirements", "nonscorm.field.mobile_platform": "Mobile Platform", "nonscorm.field.app_store_url": "App Store URL", "nonscorm.field.minimum_os_version": "Minimum OS Version", "nonscorm.field.progress_tracking": "Progress Tracking", "nonscorm.field.assessment_integration": "Assessment Integration", "nonscorm.placeholder.select_content_type": "Select Content Type", "nonscorm.placeholder.select_framework": "Select Framework", "nonscorm.framework.react": "React", "nonscorm.framework.angular": "Angular", "nonscorm.framework.vue": "Vue.js", "nonscorm.framework.vanilla": "Vanilla JavaScript", "nonscorm.flash.local": "Local", "nonscorm.flash.network": "Network", "nonscorm.unity.webgl": "WebGL", "nonscorm.unity.webplayer": "Web Player", "nonscorm.compression.gzip": "Gzip", "nonscorm.compression.brotli": "<PERSON><PERSON><PERSON>", "nonscorm.compression.none": "None", "nonscorm.mobile.cross_platform": "Cross-Platform", "nonscorm.mobile.ios": "iOS", "nonscorm.mobile.android": "Android", "js.validation.invalid_flash_version": "Please enter a valid Flash version (e.g., 11.2.0).", "js.validation.invalid_unity_version": "Please enter a valid Unity version (e.g., 2022.3.0f1).", "js.validation.invalid_os_version": "Please enter a valid OS version (e.g., iOS 14.0, Android 8.0).", "js.validation.version_required": "Version is required.", "js.validation.content_type_required": "Content type is required.", "js.validation.nonscorm_title_required": "Non-SCORM title is required.", "js.validation.nonscorm_tags_required": "Tags are required.", "js.validation.nonscorm_file_required": "File is required.", "js.validation.nonscorm_url_required": "URL is required.", "js.validation.nonscorm_url_invalid": "Please enter a valid URL (e.g., https://example.com).", "js.validation.flash_version_required": "Flash version is required.", "js.validation.unity_version_required": "Unity version is required.", "js.validation.mobile_platform_required": "Mobile platform is required.", "js.validation.app_store_url_invalid": "Please enter a valid App Store URL.", "js.validation.minimum_os_version_required": "Minimum OS version is required.", "_comment30": "JavaScript Custom Field Validation Messages", "js.validation.field_name_format": "Field name must start with a letter and contain only letters, numbers, and underscores", "js.validation.field_label_exists": "A field with this label already exists", "js.validation.field_options_required": "Field options are required for this field type", "js.validation.field_options_one_required": "At least one option is required", "js.validation.field_options_two_required": "At least two options are required", "js.validation.field_required": "{field} is required", "js.validation.field_label_min_length": "Field label must be at least 2 characters long", "js.validation.fix_errors_before_submit": "Please fix all validation errors before submitting the form.", "js.creating": "Creating...", "js.network_error": "Network error. Please try again.", "_comment31": "JavaScript Custom Field Confirmation Messages", "js.confirm_delete_custom_field": "Are you sure you want to delete the custom field \"{fieldName}\"?", "js.delete_field_with_data_warning": "⚠️ This field contains data from {usageCount} user(s). If you delete this field, all user data entered in this field will be permanently lost and cannot be recovered.\\n\\nThis will hide the field from forms and existing data will be lost. Users will not be able to see or edit this field.", "js.delete_field_no_data_warning": "This will hide the field from forms and existing data will be lost. Users will not be able to see or edit this field.", "js.confirm_deactivate_custom_field": "Are you sure you want to deactivate the custom field \"{fieldName}\"?", "js.deactivate_field_warning": "This will hide the field from forms but preserve existing data. Users will not be able to see or edit this field.", "js.confirm_activate_custom_field": "Are you sure you want to activate the custom field \"{fieldName}\"?", "js.activate_field_warning": "This will make the field visible in forms again and users will be able to fill it out."}